<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <div class="icon-with-text">
            <i class="fas fa-file-alt"></i>
            <h2>{{ mainTitle || '会议资料' }}</h2>
          </div>
        </div>

        <!-- 加载状态和错误提示 -->
        <LoadingIndicator :show="isLoading" text="正在加载资料..." />
        <ErrorMessage
            :show="hasError && !isLoading"
            type="warning"
            :message="errorMessage"
            :show-retry="true"
            @retry="refreshData"
            @close="clearError"
        />

        <!-- 资料分类 -->
        <div class="material-categories">
          <button
              class="category-btn"
              :class="{ active: activeCategory === 'all' }"
              @click="switchCategory('all')"
          >
            全部
          </button>
          <button
              class="category-btn"
              :class="{ active: activeCategory === 'presentation' }"
              @click="switchCategory('presentation')"
          >
            演讲资料
          </button>
          <button
              class="category-btn"
              :class="{ active: activeCategory === 'document' }"
              @click="switchCategory('document')"
          >
            会议文档
          </button>
          <button
              class="category-btn"
              :class="{ active: activeCategory === 'handbook' }"
              @click="switchCategory('handbook')"
          >
            参会手册
          </button>
        </div>

        <!-- 资料列表（带滚动） -->
        <div class="materials-scroll-container">
          <div class="materials-list" id="materialsList">
            <div class="material-item" v-for="material in filteredMaterials" :key="material.id">
              <div class="material-header">
                <div class="material-icon">
                  <i :class="getFileIcon(material.url)"></i>
                </div>
                <div class="material-info">
                  <h3>{{ material.title }}<span class="material-tag">{{ getFileFormatter(material.url) }}</span></h3>
                  <div class="material-meta">
                    <!-- <span><i class="fas fa-calendar"></i> {{ material.date }}</span> -->
                    <!-- <span><i class="fas fa-download"></i> {{ material.downloads || 0 }}次</span> -->
                  </div>
                </div>
              </div>
              <div class="material-description">
                {{ material.description }}
              </div>
              <div class="material-actions">
                <button class="action-btn download-btn" @click="downloadMaterial(material.id)">
                  <i class="fas fa-download"></i>
                  下载
                </button>
                <button class="action-btn preview-btn" @click="previewMaterial(material.id)">
                  <i class="fas fa-eye"></i>
                  预览
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="materials-stats">
          <div class="stat-item">
            <i class="fas fa-file-alt"></i>
            <span>总资料数：<strong id="totalMaterials">{{ totalMaterials }}</strong></span>
          </div>
          <!-- <div class="stat-item">
            <i class="fas fa-download"></i>
            <span>下载次数：<strong id="totalDownloads">{{ totalDownloads }}</strong></span>
          </div> -->
        </div>
      </div>
    </main>
    </div>

    <!-- PDF预览模态框 - 放在页面容器外部 -->
    <PdfModal
      :visible="pdfModalVisible"
      :pdf-url="currentPdfUrl"
      :title="currentPdfTitle"
      @close="closePdfModal"
      class="materials-pdf-modal"
    />
</template>

<script>
import { getList } from '@/api/materials/materials';
import { dataTransformers } from '@/utils/apiHelper';
import apiMixin from '@/mixins/apiMixin';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import { getDictionary } from '@/api/system/dictbiz';
import PdfModal from '@/components/PdfModal.vue';
import { downloadFileBlob } from '@/utils/util';
import axios from 'axios';
import * as dd from 'dingtalk-jsapi';

export default {
  name: 'Materials',
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
    PdfModal
  },
  data() {
    return {
      mainTitle: '',
      subTitle: '',
      materials: [],
      activeCategory: 'all',
      dataSource: 'unknown',
      responseTime: 0,
      // PDF预览相关
      pdfModalVisible: false,
      currentPdfUrl: '',
      currentPdfTitle: '',
      // 默认资料数据 - 按照demo样式重新组织
      defaultMaterialsData: [
        {
          id: '1',
          title: '数智攀登管理跃升主题演讲',
          category: 'presentation',
          type: 'PPT',
          icon: 'fas fa-file-powerpoint',
          date: '2025-09-15',
          size: '15.2MB',
          downloads: 156,
          description: '企业数字化转型与智能化管理的创新实践分享，包含最新的管理理念和实施方案。'
        },
        {
          id: '2',
          title: '企业管理现场会议程安排',
          category: 'document',
          type: 'PDF',
          icon: 'fas fa-file-pdf',
          date: '2025-09-10',
          size: '2.8MB',
          downloads: 234,
          description: '详细的会议日程安排，包含所有演讲主题、时间安排和会场信息。'
        },
        {
          id: '3',
          title: '参会人员手册',
          category: 'handbook',
          type: 'PDF',
          icon: 'fas fa-book',
          date: '2025-09-08',
          size: '8.5MB',
          downloads: 189,
          description: '参会须知、交通指南、住宿信息、联系方式等重要信息汇总。'
        },
        {
          id: '4',
          title: '数字化转型案例分析',
          category: 'presentation',
          type: 'PPT',
          icon: 'fas fa-file-powerpoint',
          date: '2025-09-14',
          size: '22.6MB',
          downloads: 127,
          description: '行业领先企业数字化转型成功案例深度剖析，包含实施路径与效果评估。'
        },
        {
          id: '5',
          title: '2025年度管理目标白皮书',
          category: 'document',
          type: 'PDF',
          icon: 'fas fa-file-pdf',
          date: '2025-09-05',
          size: '4.3MB',
          downloads: 312,
          description: '公司2025年度管理战略规划与目标分解，包含关键绩效指标与考核办法。'
        }
      ]
    }
  },
  computed: {
    allMaterials() {
      return this.materials.length > 0 ? this.materials : this.defaultMaterialsData;
    },
    filteredMaterials() {
      return this.activeCategory === 'all'
          ? this.allMaterials
          : this.allMaterials.filter(material => material.category === this.activeCategory);
    },
    totalMaterials() {
      return this.allMaterials.length;
    },
    totalDownloads() {
      return this.allMaterials.reduce((sum, material) => sum + (material.downloads || 0), 0);
    }
  },
  async mounted() {
    await this.loadMaterialsData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      const response = await getDictionary({ code: 'hy_materials' });
      if (response && response.data && response.data.success) {
        const dictData = response.data.data;
        if (dictData && Array.isArray(dictData) && dictData.length > 0) {
          this.mainTitle = dictData.find(item => item.dictValue === '主标题')?.dictKey;
          this.subTitle = dictData.find(item => item.dictValue === '副标题')?.dictKey;
        }
      } else {
        throw new Error('API响应格式不正确');
      }
    },
    async loadMaterialsData() {
      const startTime = Date.now();
      try {
        const response = await getList(1, 20, {});
        if (response && response.data && response.data.success) {
          this.materials = dataTransformers.materials(response.data);
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }
        this.responseTime = Date.now() - startTime;
      } catch (error) {
        console.error('加载资料数据失败:', error);
        this.materials = this.defaultMaterialsData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },
    refreshData() {
      this.loadMaterialsData();
    },
    formatApiData(data, type) {
      return type === 'array' && Array.isArray(data)
          ? dataTransformers.materials(data)
          : data;
    },
    switchCategory(category) {
      this.activeCategory = category;
    },
    getFileFormatter(fileUrlOrType) {
      if (fileUrlOrType && fileUrlOrType.includes('.')) {
        const extension = fileUrlOrType.split('.').pop().toLowerCase();
        const extensionMap = {
          'pdf': 'PDF',
          'ppt': 'PPT',
          'pptx': 'PPT',
          'xls': 'XLSX',
          'xlsx': 'XLSX',
          'doc': 'DOC',
          'docx': 'DOC',
          'zip': 'ZIP',
          'rar': 'ZIP'
        };
        return extensionMap[extension] || fileUrlOrType;
      }
      return 'PDF';
    },
    getFileIcon(fileUrlOrType) {
      let fileType = fileUrlOrType;
      if (fileUrlOrType && fileUrlOrType.includes('.')) {
        const extension = fileUrlOrType.split('.').pop().toLowerCase();
        const extensionMap = {
          'pdf': 'PDF',
          'ppt': 'PPT',
          'pptx': 'PPT',
          'xls': 'XLSX',
          'xlsx': 'XLSX',
          'doc': 'DOC',
          'docx': 'DOC',
          'zip': 'ZIP',
          'rar': 'ZIP'
        };
        fileType = extensionMap[extension] || fileUrlOrType;
      }
      const iconMap = {
        'PDF': 'fas fa-file-pdf',
        'PPT': 'fas fa-file-powerpoint',
        'XLSX': 'fas fa-file-excel',
        'DOC': 'fas fa-file-word',
        'ZIP': 'fas fa-file-archive'
      };
      return iconMap[fileType] || 'fas fa-file';
    },
    async downloadMaterial(materialId) {
      const material = this.allMaterials.find(m => m.id === materialId);
      if (material) {
        console.log(`下载资料: ${material.title}`);

        if (!material.url) {
          if (this.$message) {
            this.$message.warning('该资料暂无下载链接');
          } else {
            alert('该资料暂无下载链接');
          }
          return;
        }

        try {
          // 增加下载次数
          material.downloads = (material.downloads || 0) + 1;

          // 获取文件名
          const fileName = material.title + '.pdf';

          // 处理URL，确保是完整的绝对路径
          let downloadUrl = material.url;
          if (downloadUrl && !downloadUrl.startsWith('http')) {
            // 如果是相对路径，转换为绝对路径
            const baseUrl = window.location.origin; // 获取当前域名
            downloadUrl = baseUrl + downloadUrl;
          }

          // 直接使用钉钉JSAPI下载
          console.log('开始钉钉下载:', downloadUrl, fileName);
          console.log('处理后URL:', downloadUrl);

          // 检查钉钉环境
          if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.downloadFile) {
            // ✅ 第一步：确保 ZTNA 认证已完成
            await this.ensureZTNAAuth();
            console.log('ZTNA 认证通过，开始调用 dd.downloadFile');
            dd.downloadFile({
              url: downloadUrl,
              name: fileName,
              onSuccess: (result) => {
                console.log('钉钉下载成功:', result);
                if (this.$message) {
                  this.$message.success(`文件下载成功：${material.title}`);
                }

                // 下载成功后自动预览
                if (result.filePath) {

                  dd.openDocument({
                    filePath: result.filePath,
                    fileType: 'pdf',
                    success: (openResult) => {
                    },
                    fail: (openErr) => {
                      console.error('文档预览失败:', openErr);
                      if (this.$message) {
                        this.$message.info(`文件已下载，可在钉钉文件中查看：${material.title}`);
                      }
                    },
                    complete: () => {
                      console.log('openDocument 操作完成');
                    }
                  });
                }
              },
              onFail: (err) => {
                console.error('钉钉下载失败:', err);
                downloadFileBlob(material.url, fileName);
                if (this.$message) {
                  this.$message.warning(`钉钉下载失败，已切换到普通下载：${material.title}`);
                }
              }
            });
          } else {
            console.log('钉钉JSAPI不可用，使用普通下载');
            downloadFileBlob(material.url, fileName);
            if (this.$message) {
              this.$message.success(`正在下载：${material.title}`);
            }
          }
        } catch (error) {
          console.error('下载失败:', error);
          if (this.$message) {
            this.$message.error('下载失败，请稍后重试');
          } 
        }
      }
    },
    //确保 ZTNA 认证已完成（通过 requestAuthCode 触发）
    ensureZTNAAuth() {
      return new Promise((resolve, reject) => {
        dd.ready(() => {
          dd.runtime.permission.requestAuthCode({
            corpId: import.meta.env.VITE_DING_CORP_ID, 
            onSuccess: (result) => {
              console.log('ZTNA 认证成功，authCode:', result.code);
              resolve(result.code);
            },
            onFail: (err) => {
              console.error('ZTNA 认证失败:', err);
              reject(err);
            }
          });
        });

        // 设置超时，避免卡住
        setTimeout(() => {
          reject(new Error('ZTNA 认证超时'));
        }, 10000);
      });
    },


    /**
     * 预览资料
     */
    previewMaterial(materialId) {
      const material = this.allMaterials.find(m => m.id === materialId);
      if (material) {
        console.log(`预览资料: ${material.title}`);

        if (!material.url) {
          if (this.$message) {
            this.$message.warning('该资料暂无预览链接');
          } else {
            alert('该资料暂无预览链接');
          }
          return;
        }

        // 打开PDF预览模态框
        this.currentPdfUrl = material.url;
        this.currentPdfTitle = material.title;
        this.pdfModalVisible = true;
      }
    },

    /**
     * 关闭PDF预览模态框
     */
     closePdfModal() {
      this.pdfModalVisible = false;
      this.currentPdfUrl = '';
      this.currentPdfTitle = '';
    },
    clearError() {
      this.hasError = false;
      this.errorMessage = '';
    }
  }
}
</script>

<style scoped>
/* 页面基础样式 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 标题区域样式 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.icon-with-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 8px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.icon-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 资料分类按钮样式 */
.material-categories {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  overflow-x: auto;
  padding: 5px 0;
}

.category-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07D3F0;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.category-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.category-btn.active,
.category-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 滚动容器样式 */
.materials-scroll-container {
  max-height: calc(85vh - 280px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 20px;
}

/* 滚动条美化 */
.materials-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.materials-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.materials-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.materials-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 资料列表样式 */
.materials-list {
  margin: 10px 0;
}

.material-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.material-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.material-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.material-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.material-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.material-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border: 1px solid rgba(7, 211, 240, 0.2);
}

.material-icon i {
  color: #07D3F0;
  font-size: 20px;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.material-info {
  flex: 1;
}

.material-info h3 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.material-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.material-meta span {
  display: flex;
  align-items: center;
  gap: 3px;
}

.material-meta i {
  color: #07D3F0;
}

.material-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.4;
  margin: 15px 0;
}

.material-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.action-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.download-btn {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.preview-btn {
  background: rgba(255, 255, 255, 0.08);
  color: #07D3F0;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

.material-tag {
  background: rgba(7, 211, 240, 0.1);
  color: #07D3F0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  margin-left: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
}

/* 统计信息样式 */
.materials-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-item i {
  color: #07D3F0;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.stat-item strong {
  color: #ffffff;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .materials-scroll-container {
    max-height: calc(85vh - 280px);
  }

  .material-item {
    padding: 12px;
  }

  .material-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .material-info h3 {
    font-size: 14px;
  }
}
</style>
