<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录错误调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
            cursor: pointer;
            border: none;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .error-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>登录错误调试页面</h1>
        
        <h2>问题描述</h2>
        <p>当前登录失败时输出：<code>{"error_description": "匹配不到对应账号"}</code>，但不会跳转到403页面。</p>
        
        <h2>测试链接</h2>
        <a href="/dinglogin" class="test-btn">钉钉登录页面</a>
        <a href="/mobile-403" class="test-btn">移动端403页面</a>
        <a href="/403" class="test-btn">桌面端403页面</a>
        
        <h2>错误处理逻辑</h2>
        <div class="error-demo">
            <h3>当前实现的错误检测代码：</h3>
            <pre>
// 获取错误信息，支持多种格式
let errorMessage = ''

if (error.message) {
  errorMessage = error.message
} else if (error.error_description) {
  errorMessage = error.error_description
} else if (typeof error === 'string') {
  errorMessage = error
}

console.log('错误信息:', errorMessage)

// 检查是否是"匹配不到对应账号"错误
if (errorMessage && (errorMessage.includes('匹配不到对应账号') || errorMessage.includes('匹配不到账号'))) {
  console.log('检测到账号匹配错误，跳转到403页面')
  router.push('/mobile-403')
  return
}
            </pre>
        </div>
        
        <h2>调试步骤</h2>
        <ol>
            <li>打开浏览器开发者工具的Console面板</li>
            <li>访问钉钉登录页面</li>
            <li>触发登录失败（使用未授权的钉钉账号）</li>
            <li>查看Console中的错误信息输出</li>
            <li>确认错误检测逻辑是否正确执行</li>
        </ol>
        
        <h2>可能的问题和解决方案</h2>
        <ul>
            <li><strong>错误信息格式问题</strong>：后端返回的错误可能被axios拦截器处理过</li>
            <li><strong>错误传递问题</strong>：错误信息在从store传递到组件的过程中可能丢失</li>
            <li><strong>字符串匹配问题</strong>：错误信息的具体内容可能与预期不符</li>
        </ul>
        
        <button class="test-btn" onclick="testErrorDetection()">测试错误检测逻辑</button>
        
        <div id="test-result"></div>
    </div>
    
    <script>
        function testErrorDetection() {
            const testCases = [
                '匹配不到对应账号',
                '匹配不到账号',
                '{"error_description": "匹配不到对应账号"}',
                'Error: 匹配不到对应账号',
                '其他错误信息'
            ];
            
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<h3>测试结果：</h3>';
            
            testCases.forEach((testCase, index) => {
                const shouldMatch = testCase.includes('匹配不到对应账号') || testCase.includes('匹配不到账号');
                const result = shouldMatch ? '✅ 应该跳转到403页面' : '❌ 不应该跳转';
                
                resultDiv.innerHTML += `
                    <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <strong>测试用例 ${index + 1}:</strong> ${testCase}<br>
                        <strong>结果:</strong> ${result}
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
