import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/axios'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'

export const useUserStore = defineStore('user', () => {
  const accessToken = ref(null)
  const refreshToken = ref(null)
  const isLogin = ref(false)
  const userInfo = ref(null)
  const router = useRouter()

  // 常量定义
  const TOKEN_REFRESH_ADVANCE_TIME = 5 * 60 * 1000 // 提前5分钟刷新
  const DEFAULT_TOKEN_EXPIRES_IN = 3600 // 默认token有效期60分钟
  const REFRESH_CHECK_INTERVAL = 5 * 60 * 1000 // 定时检查间隔5分钟

  // 计算token过期时间
  function calculateTokenExpiresAt(expiresIn = DEFAULT_TOKEN_EXPIRES_IN) {
    return Date.now() + (expiresIn * 1000)
  }

  // 检查登录状态并在需要时跳转登录页
  function checkAuthAndRedirect() {
    // 如果已经登录，直接返回
    if (isLogin.value && accessToken.value) {
      return true;
    }

    // 尝试从localStorage恢复登录状态
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      try {
        const parsedUserInfo = JSON.parse(storedUserInfo);
        if (parsedUserInfo.access_token) {
          // 恢复登录状态
          initUserInfo();
          return true;
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
        localStorage.removeItem('userInfo');
      }
    }

    // 如果没有登录信息，跳转到登录页
    redirectToLogin();
    return false;
  }

  async function login() {
    return new Promise((resolve, reject) => {
      dd.getAuthCode({
        corpId: import.meta.env.VITE_DING_CORP_ID
      })
      .then((res) => {
        console.log(res.code)
        let authCode = res.code
        request({
          url: '/api/dingapp/user/login',
          method: 'post',
          data: {
            authCode: authCode
          }
        }).then((res) => {
          console.log('登录成功', res)
          // 根据项目的axios配置，res可能是完整响应对象或已提取的数据
          // 如果res有data属性，说明是完整响应对象，需要提取data
          const responseData = res.data ? res.data : res
          handleLoginSuccess(responseData)
          resolve()
        }).catch((err) => {
          console.log('登录请求失败:', err)
          console.log('登录失败')

          // 创建包含完整错误信息的错误对象
          const errorObj = new Error(err.message || '登录失败')

          // 保留原始错误信息的各种可能字段
          errorObj.error_description = err.message || err.error_description || '登录失败'
          errorObj.originalError = err

          console.log('传递给前端的错误对象:', errorObj)
          reject(errorObj)
        })
      })
    })
  }

  function handleLoginSuccess(response, redirect) {
    accessToken.value = response.access_token
    refreshToken.value = response.refresh_token
    isLogin.value = true

    // 存储用户信息
    userInfo.value = response

    // 计算token过期时间
    const tokenExpiresIn = response.expires_in || DEFAULT_TOKEN_EXPIRES_IN
    const tokenExpiresAt = calculateTokenExpiresAt(tokenExpiresIn)

    // 将token过期时间添加到用户信息中
    userInfo.value.token_expires_at = tokenExpiresAt

    // 将用户信息保存到localStorage，以便刷新页面后恢复
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

    startRefreshTokenTask()

    // 立即执行路由跳转，不使用setTimeout
    if (redirect) {
      router.replace(redirect)
    } else {
      router.replace('/mobile')
    }
  }

  // 刷新token方法
  async function refreshAccessToken() {
    if (!refreshToken.value) {
      throw new Error('没有refresh token')
    }

    const formData = new FormData();
    formData.append('grant_type', 'refresh_token');
    formData.append('refresh_token', refreshToken.value);
    formData.append('scope', 'all');
    formData.append('tenantId', '000000');

    const res = await request({
      method: 'POST',
      url: '/api/blade-auth/oauth/token',
      data: formData,
      headers: {
        'Tenant-Id': '000000',
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    // 根据项目的axios配置，res可能是完整响应对象或已提取的数据
    const responseData = res.data ? res.data : res

    // 更新token
    accessToken.value = responseData.access_token
    refreshToken.value = responseData.refresh_token

    // 计算新token的过期时间
    const tokenExpiresIn = responseData.expires_in || DEFAULT_TOKEN_EXPIRES_IN
    const tokenExpiresAt = calculateTokenExpiresAt(tokenExpiresIn)

    // 更新localStorage中的用户信息
    if (userInfo.value) {
      userInfo.value.access_token = responseData.access_token
      userInfo.value.refresh_token = responseData.refresh_token
      userInfo.value.token_expires_at = tokenExpiresAt
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    }

    return responseData.access_token
  }

  // 检查token是否即将过期（提前5分钟刷新）
  function isTokenExpiringSoon() {
    if (!userInfo.value || !userInfo.value.token_expires_at) {
      return false
    }

    const now = Date.now()
    const expiresAt = userInfo.value.token_expires_at

    return (expiresAt - now) <= TOKEN_REFRESH_ADVANCE_TIME
  }

  // 检查token是否已过期
  function isTokenExpired() {
    if (!userInfo.value || !userInfo.value.token_expires_at) {
      return true
    }

    return Date.now() >= userInfo.value.token_expires_at
  }

  // 确保token有效性（在请求前调用）
  async function ensureTokenValid() {
    if (!isLogin.value || !accessToken.value) {
      throw new Error('用户未登录')
    }

    // 如果token已过期，尝试刷新
    if (isTokenExpired()) {
      try {
        await refreshAccessToken()
      } catch (error) {
        console.error('Token刷新失败:', error)
        throw error
      }
    }
    // 如果token即将过期，提前刷新
    else if (isTokenExpiringSoon()) {
      try {
        await refreshAccessToken()
      } catch (error) {
        console.error('Token提前刷新失败:', error)
        // 提前刷新失败不抛出错误，让当前token继续使用
      }
    }
  }

  function startRefreshTokenTask() {
    setInterval(async () => {
      if (!isLogin.value) return

      try {
        // 使用新的检查逻辑
        if (isTokenExpiringSoon() || isTokenExpired()) {
          await refreshAccessToken()
        }
      } catch (error) {
        console.error('定时刷新token失败:', error)
      }
    }, REFRESH_CHECK_INTERVAL) // 5分钟检查一次
  }

  // 初始化时从localStorage恢复用户信息
  function initUserInfo() {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        const parsedUserInfo = JSON.parse(storedUserInfo)
        userInfo.value = parsedUserInfo

        // 恢复token信息
        if (parsedUserInfo.access_token) {
          accessToken.value = parsedUserInfo.access_token
          refreshToken.value = parsedUserInfo.refresh_token
          isLogin.value = true

          // 检查token是否已过期
          if (isTokenExpired()) {
            clearUserInfo()
            return
          }

          // 启动定时刷新任务
          startRefreshTokenTask()
        }
      } catch (e) {
        console.error('解析用户信息失败:', e)
        // 清除损坏的数据
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 清除用户信息
  function clearUserInfo() {
    userInfo.value = null
    accessToken.value = null
    refreshToken.value = null
    isLogin.value = false
    localStorage.removeItem('userInfo')
  }

  // 跳转到登录页
  function redirectToLogin() {
    // 保存当前路径
    const currentPath = window.location.pathname + window.location.search
    localStorage.setItem('redirectPath', currentPath)

    // 清除用户信息
    clearUserInfo()

    // 跳转到钉钉登录页
    setTimeout(() => {
    if (router) {
    router.replace('/dinglogin')
    } else {
    window.location.href = '/dinglogin'
    }
}, 100)
  }

  // 初始化用户信息
  initUserInfo()

  return {
    accessToken,
    refreshToken,
    isLogin,
    userInfo,
    login,
    handleLoginSuccess,
    clearUserInfo,
    refreshAccessToken,
    initUserInfo,
    redirectToLogin,
    checkAuthAndRedirect,
    ensureTokenValid,
    isTokenExpired,
    isTokenExpiringSoon
  }
})