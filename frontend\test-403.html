<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试403页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>测试页面</h1>
        <p>点击下面的链接测试不同的页面：</p>
        
        <a href="/dinglogin" class="test-btn">钉钉登录页面</a><br>
        <a href="/mobile-403" class="test-btn">移动端403页面</a><br>
        <a href="/403" class="test-btn">桌面端403页面</a><br>
        <a href="/mobile" class="test-btn">移动端主页</a><br>
        
        <h2>功能说明</h2>
        <p>当钉钉登录时后端返回"匹配不到对应账号"错误时，系统会自动跳转到移动端403页面。</p>
        
        <h3>实现的功能：</h3>
        <ul>
            <li>✅ 创建了移动端友好的403错误页面</li>
            <li>✅ 在路由中添加了 /mobile-403 路由</li>
            <li>✅ 修改了钉钉登录页面的错误处理逻辑</li>
            <li>✅ 当检测到"匹配不到账号"错误时自动跳转到403页面</li>
        </ul>
        
        <h3>错误检测逻辑：</h3>
        <pre>
// 检查是否是"匹配不到对应账号"错误
const errorMessage = error.message || error.error_description || ''
if (errorMessage.includes('匹配不到对应账号') || errorMessage.includes('匹配不到账号')) {
  // 跳转到移动端403页面
  router.push('/mobile-403')
  return
}
        </pre>
    </div>
</body>
</html>
